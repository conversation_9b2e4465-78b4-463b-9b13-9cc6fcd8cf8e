# AI Chatbot Setup Guide

This guide will help you set up the AI chatbot that uses Google's Gemini 2.0 Flash model.

## Prerequisites

1. **Google AI API Key**: You need a Gemini API key from Google AI Studio
2. **Environment Variables**: The chatbot fetches the API key from system environment variables

## Step 1: Get Your Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

## Step 2: Set Environment Variable

### For Windows (PowerShell):
```powershell
# Temporary (current session only)
$env:GEMINI_API_KEY="your-api-key-here"

# Permanent (system-wide)
[Environment]::SetEnvironmentVariable("GEMINI_API_KEY", "your-api-key-here", "User")
```

### For Windows (Command Prompt):
```cmd
# Temporary (current session only)
set GEMINI_API_KEY=your-api-key-here

# Permanent (system-wide) - use System Properties > Environment Variables
```

### For macOS/Linux:
```bash
# Temporary (current session only)
export GEMINI_API_KEY="your-api-key-here"

# Permanent (add to ~/.bashrc, ~/.zshrc, or ~/.profile)
echo 'export GEMINI_API_KEY="your-api-key-here"' >> ~/.bashrc
source ~/.bashrc
```

### For Development (.env file):
Create a `.env` file in the project root:
```env
VITE_GEMINI_API_KEY=your-api-key-here
```

## Step 3: Verify Setup

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Test the chatbot**:
   - Look for the floating chat button in the bottom-right corner
   - Click it or press `Ctrl+K` (or `Cmd+K` on Mac) to open
   - If properly configured, you'll see a green notification dot

3. **Run the test script** (optional):
   - Open browser console
   - Type: `new window.ChatbotTester().runAllTests()`
   - Check the test results

## Features

### Smart Navigation
The AI chatbot can automatically navigate to relevant sections based on conversation context:
- Ask about "projects" → navigates to `/projects`
- Ask about "experience" → navigates to `/experience`
- Ask about "contact" → navigates to `/contact`
- Ask about "skills" → navigates to `/about`

### Keyboard Shortcuts
- `Ctrl+K` (or `Cmd+K`): Toggle chatbot
- `Escape`: Close chatbot
- `Enter`: Send message
- `Shift+Enter`: New line in message

### Responsive Design
- Mobile-friendly interface
- Dark/light theme support
- Glass morphism design matching the website theme

## Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY not found" error**:
   - Verify the environment variable is set correctly
   - Restart your terminal/IDE after setting the variable
   - For development, ensure `.env` file is in the project root

2. **API key not working**:
   - Verify the API key is correct and active
   - Check if you have sufficient quota/credits
   - Ensure the API key has the necessary permissions

3. **Chatbot not responding**:
   - Check browser console for errors
   - Verify internet connection
   - Try refreshing the page

4. **Navigation not working**:
   - Ensure React Router is properly configured
   - Check that all route paths exist

### Debug Mode

To enable debug mode and see detailed logs:
1. Open browser console
2. Look for Gemini service initialization logs
3. Check for any error messages

### Test Panel

For development, you can add the test panel to any page:
```tsx
import ChatbotTestPanel from '@/components/debug/ChatbotTestPanel';

// Add to your component
<ChatbotTestPanel />
```

## API Usage and Costs

- The chatbot uses Google's Gemini 2.0 Flash model
- Check [Google AI pricing](https://ai.google.dev/pricing) for current rates
- The implementation includes conversation history (last 5 messages) for context
- Each message generates one API call

## Security Notes

- Never commit API keys to version control
- Use environment variables for production deployment
- Consider implementing rate limiting for production use
- The current implementation fetches API keys client-side for development convenience

## Production Deployment

For production deployment:
1. Set `GEMINI_API_KEY` environment variable on your hosting platform
2. Consider implementing server-side API proxy for better security
3. Add rate limiting and usage monitoring
4. Implement proper error handling and fallbacks

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your API key and environment setup
3. Run the test script to diagnose problems
4. Check the [Google AI documentation](https://ai.google.dev/docs) for API-related issues
