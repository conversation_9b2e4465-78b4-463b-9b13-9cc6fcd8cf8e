@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: 'Inter var', system-ui, sans-serif;
    line-height: 1.5;
    font-weight: 400;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-primary-50 dark:bg-primary-950;
    @apply bg-gray-100 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
    @apply min-h-screen transition-colors duration-300;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  h5 {
    @apply text-lg md:text-xl;
  }

  h6 {
    @apply text-base md:text-lg;
  }

  a {
    @apply text-primary-700 hover:text-primary-800 dark:text-primary-300 dark:hover:text-primary-200 transition-colors;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section {
    @apply py-16 md:py-24;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 hover:shadow-md dark:bg-primary-700 dark:hover:bg-primary-600;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 hover:shadow-md dark:bg-secondary-700 dark:hover:bg-secondary-600;
  }

  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-50 dark:border-primary-400 dark:text-primary-400 dark:hover:bg-primary-900/30;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border border-gray-200 dark:border-gray-700;
  }

  .glass {
    @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50;
  }
}

@layer utilities {
  .animate-delay-100 {
    animation-delay: 100ms;
  }
  .animate-delay-200 {
    animation-delay: 200ms;
  }
  .animate-delay-300 {
    animation-delay: 300ms;
  }
  .animate-delay-400 {
    animation-delay: 400ms;
  }
  .animate-delay-500 {
    animation-delay: 500ms;
  }
  .animate-delay-600 {
    animation-delay: 600ms;
  }
  .animate-delay-700 {
    animation-delay: 700ms;
  }
  .animate-delay-800 {
    animation-delay: 800ms;
  }
  .animate-delay-900 {
    animation-delay: 900ms;
  }
  .animate-delay-1000 {
    animation-delay: 1000ms;
  }

  .animate-tilt {
    animation: tilt 10s infinite linear;
  }

  @keyframes tilt {
    0%, 50%, 100% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(1deg);
    }
    75% {
      transform: rotate(-1deg);
    }
  }

  /* AI Chatbot highlight animation */
  .ai-highlight {
    animation: ai-pulse 2s ease-in-out;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    border-radius: 8px;
  }

  @keyframes ai-pulse {
    0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }
    50% {
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
    }
  }
}
