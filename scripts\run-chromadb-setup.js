#!/usr/bin/env node

/**
 * ChromaDB Comprehensive Setup Runner
 * This script runs the Python setup script for comprehensive ChromaDB knowledge base
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧠 Setting up Comprehensive ChromaDB Knowledge Base...\n');

// Check if Python is installed
function checkPython() {
  return new Promise((resolve, reject) => {
    const python = spawn('python', ['--version']);
    
    python.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Python is installed');
        resolve('python');
      } else {
        // Try python3
        const python3 = spawn('python3', ['--version']);
        python3.on('close', (code) => {
          if (code === 0) {
            console.log('✅ Python3 is installed');
            resolve('python3');
          } else {
            console.log('❌ Python is not installed. Please install Python 3.8+ first.');
            reject(false);
          }
        });
      }
    });
  });
}

// Check if ChromaDB is installed
function checkChromaDB(pythonCmd) {
  return new Promise((resolve, reject) => {
    console.log('🔍 Checking ChromaDB installation...');
    
    const check = spawn(pythonCmd, ['-c', 'import chromadb; print("ChromaDB is installed")']);
    
    check.on('close', (code) => {
      if (code === 0) {
        console.log('✅ ChromaDB is already installed');
        resolve(true);
      } else {
        console.log('📦 ChromaDB not found, installing...');
        installChromaDB(pythonCmd).then(resolve).catch(reject);
      }
    });
  });
}

// Install ChromaDB
function installChromaDB(pythonCmd) {
  return new Promise((resolve, reject) => {
    const pip = pythonCmd === 'python' ? 'pip' : 'pip3';
    const install = spawn(pip, ['install', 'chromadb'], { stdio: 'inherit' });
    
    install.on('close', (code) => {
      if (code === 0) {
        console.log('✅ ChromaDB installed successfully');
        resolve(true);
      } else {
        console.log('❌ Failed to install ChromaDB');
        reject(false);
      }
    });
  });
}

// Run the comprehensive setup script
function runSetupScript(pythonCmd) {
  return new Promise((resolve, reject) => {
    console.log('\n🚀 Running comprehensive knowledge base setup...\n');
    
    const scriptPath = path.join(__dirname, 'setup-comprehensive-chromadb.py');
    const setup = spawn(pythonCmd, [scriptPath], { stdio: 'inherit' });
    
    setup.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ Comprehensive ChromaDB setup completed successfully!');
        resolve(true);
      } else {
        console.log('\n❌ Setup script failed');
        reject(false);
      }
    });
    
    setup.on('error', (error) => {
      console.log(`❌ Error running setup script: ${error.message}`);
      reject(false);
    });
  });
}

// Main setup function
async function setup() {
  try {
    console.log('📋 Prerequisites Check:');
    const pythonCmd = await checkPython();
    await checkChromaDB(pythonCmd);
    
    console.log('\n📊 Data Sources:');
    console.log('  • src/data/portfolio.txt - Detailed resume information');
    console.log('  • src/data/portfolio.ts - Structured TypeScript data');
    console.log('  • Existing knowledge base - Current structured documents');
    console.log('  • Professional testimonials and recommendations');
    
    await runSetupScript(pythonCmd);
    
    console.log('\n🎉 Setup Complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Start ChromaDB server: npm run chromadb:start');
    console.log('2. Start your development server: npm run dev');
    console.log('3. The AI chatbot will automatically use the enhanced knowledge base');
    console.log('\n📊 Knowledge Base Features:');
    console.log('  • 15+ comprehensive documents');
    console.log('  • Education, experience, projects, skills, testimonials');
    console.log('  • Semantic search capabilities');
    console.log('  • Persistent local storage');
    console.log('  • Professional third-person responses');
    
  } catch (error) {
    console.log('\n❌ Setup failed. Please check the requirements and try again.');
    console.log('💡 Requirements:');
    console.log('  • Python 3.8+ installed');
    console.log('  • pip package manager available');
    console.log('  • Internet connection for package installation');
  }
}

// Run setup
setup();
