import { GoogleGenerativeAI } from '@google/generative-ai';
import { portfolioKnowledgeBase } from './portfolioKnowledgeBase';
import { navigationService, NavigationAction } from './navigationService';

// Interface for chat messages
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

// Interface for navigation commands
export interface NavigationCommand {
  action: 'navigate';
  path: string;
  section?: string;
}

class GeminiService {
  private genAI: GoogleGenerativeAI | null = null;
  private model: any = null;
  private apiKey: string | null = null;

  constructor() {
    this.initializeAPI();
  }

  private initializeAPI() {
    try {
      // Fetch API key from environment variables (Vite handles import.meta.env)
      this.apiKey = import.meta.env.VITE_GEMINI_API_KEY;

      console.log('Fetching Gemini API key from environment...');
      console.log('Available env vars:', Object.keys(import.meta.env));

      if (!this.apiKey) {
        console.error('VITE_GEMINI_API_KEY not found in environment variables');
        console.log('Please set VITE_GEMINI_API_KEY in your .env file or environment');
        // Don't throw error immediately, let the service be created but mark as not ready
        return;
      }

      console.log('Gemini API key found, initializing service...');

      this.genAI = new GoogleGenerativeAI(this.apiKey);
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });

      console.log('Gemini service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Gemini API:', error);
      // Don't throw error, just log it
    }
  }

  // Get portfolio knowledge base for context
  private getPortfolioContext(): string {
    const kb = portfolioKnowledgeBase;

    return `
You are an AI assistant for Vansh Oberoi's portfolio website. You should refer to Vansh in third person and help interviewers learn about him.

PERSONAL INFO:
- Name: ${kb.personal.name}
- Title: ${kb.personal.title}
- Location: ${kb.personal.location}
- Email: ${kb.personal.email}
- Phone: ${kb.personal.phone}

SUMMARY:
${kb.personal.summary}

CURRENT EXPERIENCE:
${kb.experience.current.title} at ${kb.experience.current.company} (${kb.experience.current.duration})
Key responsibilities: ${kb.experience.current.responsibilities.slice(0, 3).join(', ')}

TOP SKILLS:
- ${kb.skills.programming.map(s => `${s.name} (${s.level}%)`).join(', ')}
- AI/ML: ${kb.skills.aiml.map(s => s.name).slice(0, 4).join(', ')}
- Frameworks: ${kb.skills.frameworks.map(s => s.name).join(', ')}
- Cloud: ${kb.skills.cloud.map(s => s.name).join(', ')}

FEATURED PROJECTS:
${kb.projects.featured.map(p => `- ${p.title}: ${p.description.substring(0, 100)}...`).join('\n')}

NAVIGATION SECTIONS AVAILABLE:
${Object.entries(kb.navigationSections).map(([path, desc]) => `- ${path} - ${desc}`).join('\n')}

COMMON TOPICS & RESPONSES:
- Skills: ${kb.commonQuestions.skills}
- Experience: ${kb.commonQuestions.experience}
- Projects: ${kb.commonQuestions.projects}
- Contact: ${kb.commonQuestions.contact}

When users ask about specific topics, you can suggest navigating to relevant sections. Always be helpful, professional, and informative about Vansh's qualifications and experience. Provide specific details from his portfolio when relevant.
`;
  }

  // Generate response with navigation awareness
  async generateResponse(
    message: string,
    chatHistory: ChatMessage[] = [],
    currentPath: string = '/'
  ): Promise<{ response: string; navigationCommand?: NavigationCommand; navigationAction?: NavigationAction }> {
    if (!this.model) {
      throw new Error('Gemini service not initialized');
    }

    try {
      // Analyze navigation intent
      const navigationIntent = navigationService.analyzeNavigationIntent(message);
      const smartNavigation = navigationService.smartNavigate(message, currentPath);

      // Build conversation context
      const context = this.getPortfolioContext();

      // Format chat history
      const historyText = chatHistory
        .slice(-5) // Keep last 5 messages for context
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      // Enhanced prompt with navigation capabilities
      const currentSection = navigationService.getCurrentSection();
      const prompt = `${context}

CURRENT SECTION: ${currentSection}
CONVERSATION HISTORY:
${historyText}

USER MESSAGE: ${message}

Please respond as Vansh Oberoi's AI assistant. Be helpful and informative about his qualifications.

NAVIGATION GUIDELINES:
- If the user asks about work/experience/career, suggest navigating to /experience
- If they ask about projects/portfolio/code, suggest navigating to /projects
- If they ask about contact/hiring/reach out, suggest navigating to /contact
- If they ask about background/personal info, suggest navigating to /about
- If they ask about skills/technologies, mention you can show them on the about page

When suggesting navigation, be natural and include phrases like:
- "Let me show you his [section]"
- "Would you like to see his [section]?"
- "I can take you to his [section] page"

Respond naturally and conversationally, referring to Vansh in third person.`;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text();

      // Parse for navigation commands (legacy support)
      let navigationCommand: NavigationCommand | undefined;

      if (response.includes('experience') && (response.includes('show') || response.includes('take you'))) {
        navigationCommand = { action: 'navigate', path: '/experience' };
      } else if (response.includes('projects') && (response.includes('show') || response.includes('take you'))) {
        navigationCommand = { action: 'navigate', path: '/projects' };
      } else if (response.includes('contact') && (response.includes('show') || response.includes('take you'))) {
        navigationCommand = { action: 'navigate', path: '/contact' };
      } else if (response.includes('about') && (response.includes('show') || response.includes('take you'))) {
        navigationCommand = { action: 'navigate', path: '/about' };
      }

      return {
        response,
        navigationCommand,
        navigationAction: smartNavigation || undefined
      };
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error('Failed to generate response. Please try again.');
    }
  }

  // Check if service is ready
  isReady(): boolean {
    return this.model !== null && this.apiKey !== null;
  }

  // Get API key status for debugging
  getApiKeyStatus(): { hasKey: boolean; source: string; envVars: any } {
    const viteKey = import.meta.env.VITE_GEMINI_API_KEY;

    if (viteKey) {
      return {
        hasKey: true,
        source: 'VITE_GEMINI_API_KEY',
        envVars: Object.keys(import.meta.env)
      };
    } else {
      return {
        hasKey: false,
        source: 'none',
        envVars: Object.keys(import.meta.env)
      };
    }
  }
}

// Export singleton instance
export const geminiService = new GeminiService();
export default geminiService;
