import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Database, Search, Tag, Calendar, Star, ChevronDown, ChevronRight } from 'lucide-react';
import { chromaKnowledgeBase, knowledgeDocuments, KnowledgeDocument } from '@/services/chromaKnowledgeBase';

interface KnowledgeBaseViewerProps {
  isOpen: boolean;
  onClose: () => void;
}

const KnowledgeBaseViewer: React.FC<KnowledgeBaseViewerProps> = ({ isOpen, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedDocs, setExpandedDocs] = useState<Set<string>>(new Set());
  const [searchResults, setSearchResults] = useState<any>(null);
  const [isSearching, setIsSearching] = useState(false);

  // Get categories
  const categories = ['all', ...chromaKnowledgeBase.getCategories()];

  // Filter documents
  const filteredDocs = knowledgeDocuments.filter(doc => {
    const matchesCategory = selectedCategory === 'all' || doc.metadata.category === selectedCategory;
    const matchesSearch = !searchQuery || 
      doc.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.metadata.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Handle search
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults(null);
      return;
    }

    setIsSearching(true);
    try {
      const results = await chromaKnowledgeBase.query(searchQuery, 5);
      setSearchResults(results);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Toggle document expansion
  const toggleExpanded = (docId: string) => {
    const newExpanded = new Set(expandedDocs);
    if (newExpanded.has(docId)) {
      newExpanded.delete(docId);
    } else {
      newExpanded.add(docId);
    }
    setExpandedDocs(newExpanded);
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    const colors = {
      personal: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      experience: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      skills: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      projects: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      expertise: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  // Get importance stars
  const getImportanceStars = (importance: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${i < importance / 2 ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-6xl h-[80vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Database className="w-6 h-6 text-primary-500" />
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  AI Knowledge Base
                </h2>
                <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full text-sm">
                  {knowledgeDocuments.length} documents
                </span>
              </div>
              <button
                onClick={onClose}
                className="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-center"
              >
                ×
              </button>
            </div>

            {/* Search and Filters */}
            <div className="mt-4 flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  placeholder="Search knowledge base..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
              <button
                onClick={handleSearch}
                disabled={isSearching}
                className="px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50"
              >
                {isSearching ? 'Searching...' : 'Search'}
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* Search Results */}
            {searchResults && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
                  Search Results ({searchResults.documents.length})
                </h3>
                <div className="space-y-3">
                  {searchResults.documents.map((doc: string, index: number) => (
                    <div key={index} className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                      <p className="text-sm text-gray-700 dark:text-gray-300">{doc}</p>
                      <div className="mt-2 flex items-center space-x-2 text-xs text-gray-500">
                        <span>Relevance: {(1 - searchResults.distances[index]).toFixed(2)}</span>
                        <span>•</span>
                        <span>ID: {searchResults.ids[index]}</span>
                      </div>
                    </div>
                  ))}
                </div>
                <hr className="my-6 border-gray-200 dark:border-gray-700" />
              </div>
            )}

            {/* Document List */}
            <div className="space-y-4">
              {filteredDocs.map((doc) => (
                <motion.div
                  key={doc.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                >
                  <div
                    className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    onClick={() => toggleExpanded(doc.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          {expandedDocs.has(doc.id) ? (
                            <ChevronDown className="w-4 h-4 text-gray-400" />
                          ) : (
                            <ChevronRight className="w-4 h-4 text-gray-400" />
                          )}
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(doc.metadata.category)}`}>
                            {doc.metadata.category}
                          </span>
                          {doc.metadata.subcategory && (
                            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full text-xs">
                              {doc.metadata.subcategory}
                            </span>
                          )}
                          <div className="flex items-center space-x-1">
                            {getImportanceStars(doc.metadata.importance)}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                          {doc.content.substring(0, 150)}...
                        </p>
                      </div>
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedDocs.has(doc.id) && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800"
                      >
                        <div className="p-4">
                          <p className="text-sm text-gray-700 dark:text-gray-300 mb-4 whitespace-pre-wrap">
                            {doc.content}
                          </p>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                            <div>
                              <span className="font-medium text-gray-900 dark:text-gray-100">Tags:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {doc.metadata.tags.map(tag => (
                                  <span key={tag} className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded">
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            </div>
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2">
                                <Calendar className="w-3 h-3 text-gray-400" />
                                <span className="text-gray-600 dark:text-gray-400">
                                  Updated: {doc.metadata.lastUpdated}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Tag className="w-3 h-3 text-gray-400" />
                                <span className="text-gray-600 dark:text-gray-400">
                                  Source: {doc.metadata.source}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>

            {filteredDocs.length === 0 && (
              <div className="text-center py-12">
                <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  No documents found matching your criteria
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default KnowledgeBaseViewer;
