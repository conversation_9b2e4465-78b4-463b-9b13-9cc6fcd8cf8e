// Comprehensive knowledge base for <PERSON>sh Oberoi's portfolio
export const portfolioKnowledgeBase = {
  personal: {
    name: '<PERSON><PERSON> Oberoi',
    title: 'AIML Development Engineer',
    location: 'Model Town, Kapurthala, Punjab 144601',
    email: '<EMAIL>',
    phone: '+91 **********',
    summary: 'Passionate AI Developer and Software Engineer with expertise in Python, Machine Learning, Django REST APIs, and Cloud Technologies. Currently working as an AI Developer & Software Developer Intern at EaseMyMed, developing cutting-edge AI solutions, RESTful APIs, implementing RAG for contextual AI interactions, and integrating advanced AI technologies including OpenAI and Gemini models.',
    socials: {
      linkedin: 'https://linkedin.com/in/Vansh462',
      github: 'https://github.com/Vansh462',
      facebook: 'https://www.facebook.com/solo.learn.33',
      twitter: 'https://twitter.com/vansh462',
      instagram: 'https://www.instagram.com/vanshoberoi3103'
    }
  },

  skills: {
    programming: [
      { name: 'Python', level: 95, description: 'Expert level with extensive experience in AI/ML development' },
      { name: 'C++', level: 85, description: 'Strong foundation in systems programming' },
      { name: 'JavaScript', level: 80, description: 'Frontend and backend development' },
      { name: 'TypeScript', level: 75, description: 'Type-safe JavaScript development' },
      { name: 'SQL', level: 85, description: 'Database design and optimization' }
    ],
    aiml: [
      { name: 'Machine Learning', level: 90, description: 'Supervised and unsupervised learning algorithms' },
      { name: 'Deep Learning', level: 85, description: 'Neural networks, CNN, RNN implementations' },
      { name: 'TensorFlow', level: 85, description: 'Deep learning model development and deployment' },
      { name: 'Scikit-learn', level: 90, description: 'Classical ML algorithms and preprocessing' },
      { name: 'Computer Vision', level: 80, description: 'OpenCV, image processing, object detection' },
      { name: 'NLP', level: 75, description: 'Text processing, sentiment analysis, language models' }
    ],
    frameworks: [
      { name: 'Django', level: 88, description: 'REST API development and web applications' },
      { name: 'React', level: 80, description: 'Modern frontend development' },
      { name: 'FastAPI', level: 75, description: 'High-performance API development' }
    ],
    cloud: [
      { name: 'AWS SageMaker', level: 60, description: 'ML model training and deployment' },
      { name: 'Google Cloud Platform', level: 80, description: 'Cloud deployment and services' },
      { name: 'Docker', level: 80, description: 'Containerization and deployment' }
    ],
    tools: [
      { name: 'Git', level: 35, description: 'Version control and collaboration' },
      { name: 'GitHub', level: 70, description: 'Code hosting and project management' },
      { name: 'VS Code', level: 90, description: 'Primary development environment' },
      { name: 'Jupyter Notebook', level: 85, description: 'Data analysis and prototyping' }
    ]
  },

  experience: {
    current: {
      title: 'AIML Development Engineer Intern',
      company: 'EaseMyMed',
      duration: 'Dec 2024 - June 2025',
      responsibilities: [
        'Developed and managed RESTful APIs using Django REST framework for healthcare applications',
        'Integrated AI technologies including OpenAI (GPT-4o-mini) and Gemini (gemini-2.0-flash) models',
        'Developed custom AI functions to deepen understanding of core principles, bypassing LangChain where applicable',
        'Implemented RAG to cross reference and give right context to AI in-between consecutive calls',
        'Integrated Bhashini AI voice models and implemented benchmarking and feedback mechanisms',
        'Developed solutions for processing and analyzing images, PDFs, ZIP folders using JSON data structures',
        'Utilized AWS SageMaker for development and Google Cloud Console for deployment with Docker and CloudBuild',
        'Created daily technical documentation on Notion and researched new feature prototypes'
      ],
      technologies: ['Python', 'Django REST Framework', 'RESTful APIs', 'RAG Pipeline', 'OpenAI API', 'Gemini API', 'AWS SageMaker', 'Google Cloud Platform', 'Docker', 'Bhashini AI', 'Notion']
    }
  },

  projects: {
    featured: [
      {
        title: 'PromptWizard',
        description: 'Designed and implemented a custom frontend UI from scratch for Microsoft\'s prompt optimizer, focusing on user flow and clarity. Integrated backend API with the new UI and made the whole app deployable and hostable via a single command (Vercel-first deployment) following mono repo structure.',
        technologies: ['Microsoft Backend', 'VibeCoded Custom UI', 'Vercel', 'API Integration', 'Mono Repo'],
        links: {
          live: 'https://prompt-wizard-three.vercel.app/',
          github: 'https://github.com/Vansh462/PromptWizard'
        },
        highlights: [
          'Custom frontend UI from scratch',
          'Vercel-first deployment strategy',
          'Test Values feature for instant previews',
          'Multi-feature selection for optimized prompts'
        ]
      },
      {
        title: 'Dr\'s Medicine Prescription Prediction',
        description: 'Built a supervised machine learning pipeline to predict appropriate medical prescriptions based on patient symptoms and demographic data. Achieved over 99% test accuracy with Random Forest model.',
        technologies: ['Python', 'pandas', 'scikit-learn', 'seaborn', 'matplotlib', 'Jupyter Notebook'],
        links: {
          kaggle: 'https://kaggle.com/code/vanshoberoi3103/dr-s-medicine-prescription-prediction-model-99'
        },
        highlights: [
          '5,900 patient records processed',
          '99% test accuracy achieved',
          'Extensive EDA and feature engineering',
          'Random Forest optimization'
        ]
      },
      {
        title: 'Jute Pest Classification',
        description: 'Fine-tuned TensorFlow\'s ResNet101x1 to classify 13 jute pest types with 95% accuracy. Optimized training on AWS m5.large instance achieving ~10s/epoch.',
        technologies: ['TensorFlow', 'ResNet101x1', 'AWS(m5.large)', 'Computer Vision', 'Deep Learning'],
        links: {
          github: 'https://github.com/Vansh462/LearningProjects/tree/main/Jute%20Pest',
          kaggle: 'https://www.kaggle.com/code/vanshoberoi3103/jute-pest-tf-restnet101x1-95-acc-on-1st-try'
        },
        highlights: [
          '95% accuracy on 13 pest types',
          'AWS cloud optimization',
          'Advanced preprocessing techniques',
          'Hyperparameter tuning expertise'
        ]
      },
      {
        title: 'Sports Person Classification',
        description: 'Engineered a face-based sports person classifier using HaarCascades and wavelet transforms. Achieved 84.31% test accuracy with Logistic Regression after systematic hyperparameter optimization.',
        technologies: ['scikit-learn', 'OpenCV(cv2)', 'matplotlib', 'GridSearchCV', 'HaarCascades', 'Wavelet'],
        highlights: [
          'Face detection and validation',
          'Wavelet transform feature extraction',
          'GridSearchCV optimization',
          '84.31% test accuracy'
        ]
      }
    ]
  },

  technologies: [
    'VS Code', 'Vim', 'Git', 'Android Studio', 'AWS SageMaker', 'AWS EC2', 'S3 buckets',
    'GCP', 'Docker', 'Scikit-learn', 'Beautiful-Soup', 'TensorFlow', 'OpenCV',
    'Computer Vision cv2', 'Linux (Kali/Arch/Mint)', 'Google-Colab', 'Django',
    'GraphDB', 'GraphRAG', 'DataStax', 'Make.com', 'LangGraph', 'Ollama',
    'OpenAI', 'Gemini', 'DeepSeek', 'GitHub Copilot', 'RooCode', 'Amazon Q Dev',
    'cursorAI', 'Napkin.ai', 'draw.io', 'Notion', 'Multiprocessing', 'Threads',
    'Streamlit', 'Pygame', 'NLTK', 'Pandas', 'NumPy', 'Matplotlib', 'Seaborn',
    'Neo4j', 'Cypher', 'Bhashini AI', 'Selenium', 'CI/CD', 'NotebookLLM',
    'ChatGPT', 'Claude'
  ],

  navigationSections: {
    '/': 'Home - Main landing page with overview and introduction',
    '/about': 'About - Detailed personal information, background, and interests',
    '/experience': 'Experience - Work history, internships, and professional achievements',
    '/projects': 'Projects - Portfolio of completed projects with demos and code',
    '/contact': 'Contact - Contact information, social links, and contact form'
  },

  commonQuestions: {
    skills: 'Vansh has expertise in Python (95%), Machine Learning (90%), Django (88%), and various AI/ML technologies. His strongest areas are Python development, AI/ML implementation, and cloud technologies.',
    experience: 'He is currently working as an AIML Development Engineer Intern at EaseMyMed, where he develops RESTful APIs, integrates AI models like OpenAI and Gemini, and implements RAG systems.',
    projects: 'His notable projects include PromptWizard (Microsoft prompt optimizer UI), medical prescription prediction (99% accuracy), jute pest classification (95% accuracy), and sports person classification.',
    education: 'While specific educational details aren\'t provided in the portfolio, his extensive technical skills and project portfolio demonstrate strong self-learning capabilities and practical experience.',
    contact: 'You can reach <NAME_EMAIL>, phone +91 **********, or through his social media profiles on LinkedIn, GitHub, and other platforms.'
  }
};

export default portfolioKnowledgeBase;
