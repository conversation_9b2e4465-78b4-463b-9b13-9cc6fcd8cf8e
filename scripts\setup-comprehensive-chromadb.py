#!/usr/bin/env python3
"""
Comprehensive ChromaDB Knowledge Base Setup Script
Extracts data from all available sources and creates a persistent ChromaDB knowledge base
"""

import os
import json
import re
import chromadb
from chromadb.config import Settings
import uuid
from datetime import datetime
from typing import List, Dict, Any

class PortfolioDataExtractor:
    def __init__(self):
        self.documents = []
        self.base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
    def extract_from_portfolio_txt(self) -> List[Dict]:
        """Extract structured data from portfolio.txt"""
        portfolio_txt_path = os.path.join(self.base_path, 'src', 'data', 'portfolio.txt')
        
        if not os.path.exists(portfolio_txt_path):
            print(f"❌ portfolio.txt not found at {portfolio_txt_path}")
            return []
            
        with open(portfolio_txt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        documents = []
        
        # Extract Education
        education_match = re.search(r'\*\*\*Education\*\*\*(.*?)\*\*\*Experience\*\*\*', content, re.DOTALL)
        if education_match:
            education_content = education_match.group(1).strip()
            documents.append({
                'id': 'education-comprehensive',
                'content': f"""Vansh Oberoi's Educational Background:

University Education:
- Guru Nanak Dev University, Amritsar, Punjab (State Government University)
- Bachelor of Technology in Computer Science & Engineering
- Duration: August 2022 – June 2026 (Expected)
- Currently pursuing his B.Tech degree

Secondary Education:
- Montgomery Guru Nanak Public School, Kapurthala, Punjab
- Class XII (12th Grade) - August 2021
- Score: 88.4% (CBSE Board)

Primary Education:
- Little Angles CO-ED Public School, Kapurthala, Punjab
- Class X (10th Grade) - August 2019
- Score: 87.4% (CBSE Board)

Academic Foundation:
Strong foundation in Advanced C++ programming, OOPS concepts (JAVA), Python, Data structures and algorithms, Design and Analysis of Algorithms, Operating system principles, and Basic networking concepts.""",
                'metadata': {
                    'category': 'education',
                    'subcategory': 'academic-background',
                    'importance': 8,
                    'tags': ['education', 'university', 'btech', 'computer-science', 'gndu', 'cbse'],
                    'lastUpdated': datetime.now().isoformat(),
                    'source': 'portfolio.txt'
                }
            })
        
        # Extract Experience (EaseMyMed)
        experience_match = re.search(r'\*\*EaseMyMed\*\*(.*?)(?=\*\*\*Projects\*\*\*)', content, re.DOTALL)
        if experience_match:
            experience_content = experience_match.group(1).strip()
            documents.append({
                'id': 'experience-easemymed-detailed',
                'content': f"""Vansh's Current Role at EaseMyMed (December 2024 – June 2025):

Position: AI Developer & Software Developer Intern (6 Months)

Key Responsibilities and Achievements:
• Developed and managed RESTful APIs using Django REST framework for healthcare applications
• Integrated multiple AI technologies including OpenAI (primarily GPT-4o-mini) and Google Gemini (gemini-2.0-flash)
• Developed custom AI functions to deepen understanding of core principles, often bypassing LangChain for more direct implementations
• Implemented RAG (Retrieval-Augmented Generation) systems to cross-reference and provide contextual AI responses in consecutive calls
• Integrated Bhashini AI voice models with benchmarking and feedback mechanisms
• Developed comprehensive solutions for processing and analyzing images, PDFs, ZIP folders using JSON data structures as standard information passers
• Utilized AWS SageMaker for development environment and Google Cloud Console for deployment
• Implemented CI/CD pipelines connecting Git repositories with GCP for automated server builds using DockerFile and CloudBuild
• Managed cloud storage files for on-cloud access and scalability
• Created daily technical documentation on Notion while researching and documenting prototypes for new features

Technical Libraries Used:
requests, json, base64, pyMUPDF, genai, PIL, and various other Python libraries for AI and data processing.""",
                'metadata': {
                    'category': 'experience',
                    'subcategory': 'current-internship',
                    'importance': 10,
                    'tags': ['easemymed', 'internship', 'django', 'ai', 'rag', 'aws', 'gcp', 'healthcare'],
                    'lastUpdated': datetime.now().isoformat(),
                    'source': 'portfolio.txt'
                }
            })
        
        return documents
    
    def extract_projects_from_txt(self) -> List[Dict]:
        """Extract detailed project information from portfolio.txt"""
        portfolio_txt_path = os.path.join(self.base_path, 'src', 'data', 'portfolio.txt')
        
        if not os.path.exists(portfolio_txt_path):
            return []
            
        with open(portfolio_txt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        documents = []
        
        # Extract PromptWizard project
        promptwizard_match = re.search(r'\*\*PromptWizard\*\*(.*?)(?=\*\*Dr\'s Medicine)', content, re.DOTALL)
        if promptwizard_match:
            documents.append({
                'id': 'project-promptwizard-detailed',
                'content': f"""PromptWizard - Microsoft Prompt Optimizer Frontend (June 2025)

Project Overview:
A custom frontend UI built from scratch for Microsoft's prompt optimizer, focusing on exceptional user flow and clarity.

Technical Implementation:
• Backend: Microsoft's existing prompt optimization engine
• Frontend: Custom UI developed by VibeCoded team
• Deployment: Vercel-first deployment strategy with mono repo structure (API and UI separate)
• Architecture: Plug-and-play deployment requiring no manual server configuration

Key Features:
• "Test Values" feature for auto-filling sample data with instant output previews for demo and debugging
• Multi-feature selection allowing users to instantly receive optimized prompts tailored to their specific needs
• End-to-end deployment achieved in under 5 hours with iterative UX improvements post-launch
• Minimal setup requirements with instant feedback and clarity for all user actions

Live Demo: https://prompt-wizard-three.vercel.app/
GitHub: Available on request

Special Notes:
- Focus on usability: minimal setup, instant feedback, clear user actions
- Achieved rapid deployment while maintaining high code quality
- Designed for scalability and ease of maintenance""",
                'metadata': {
                    'category': 'projects',
                    'subcategory': 'featured-frontend',
                    'importance': 10,
                    'tags': ['promptwizard', 'microsoft', 'frontend', 'vercel', 'ui-ux', 'deployment'],
                    'lastUpdated': datetime.now().isoformat(),
                    'source': 'portfolio.txt'
                }
            })
        
        # Extract additional projects
        projects_section = re.search(r'\*\*\*Projects\*\*\*(.*?)\*\*\*Technical Skills\*\*\*', content, re.DOTALL)
        if projects_section:
            projects_content = projects_section.group(1)
            
            # Bombay House Price Prediction
            if 'Bombay House Price Prediction' in projects_content:
                documents.append({
                    'id': 'project-house-price-prediction',
                    'content': """Bombay House Price Prediction Site & Model (August 2024)

Project Overview:
A comprehensive house price prediction system combining machine learning with web deployment.

Technical Stack:
• Backend: Python with Linear Regression ML model
• Frontend: Streamlit web application
• Deployment: AWS Cloud EC2 instance
• Server: Nginx configured as reverse proxy

Features:
• House price calculation based on multiple factors: number of rooms, town name, air conditioning, parking availability, and additional amenities
• Interactive web interface for user input and real-time predictions
• Scalable cloud deployment with efficient application serving
• Reverse proxy configuration for optimal performance

GitHub: https://github.com/Vansh462/LearningProjects/BHP

Technical Achievements:
• Successfully deployed ML model to production environment
• Implemented efficient web serving architecture
• Created user-friendly interface for non-technical users""",
                    'metadata': {
                        'category': 'projects',
                        'subcategory': 'ml-web-app',
                        'importance': 7,
                        'tags': ['machine-learning', 'streamlit', 'aws', 'nginx', 'deployment', 'real-estate'],
                        'lastUpdated': datetime.now().isoformat(),
                        'source': 'portfolio.txt'
                    }
                })
        
        return documents
    
    def extract_volunteer_experience(self) -> List[Dict]:
        """Extract volunteer and professional experience"""
        portfolio_txt_path = os.path.join(self.base_path, 'src', 'data', 'portfolio.txt')

        if not os.path.exists(portfolio_txt_path):
            return []

        with open(portfolio_txt_path, 'r', encoding='utf-8') as f:
            content = f.read()

        documents = []

        # Extract volunteer experience section
        volunteer_match = re.search(r'\*\*\*Professional & Volunteer Experience\*\*\*(.*?)(?=\*\*Academic Background\*\*|$)', content, re.DOTALL)
        if volunteer_match:
            volunteer_content = volunteer_match.group(1).strip()
            documents.append({
                'id': 'experience-volunteer-leadership',
                'content': f"""Vansh's Professional & Volunteer Experience:

Graph Database Research & Learning (2025):
• Researched and learned GraphDB and GraphRAG concepts through Neo4j workshops
• Gained proficiency in Cypher query language
• Understood the advantages of GraphDB for accurate data retrieval

Leadership Experience:
• GNDU E-Cell - Design Team Head (Spring 2022 – 2023)
  - Led a team of 4 members in creating innovative designs for diverse projects
  - Managed the design team and collaborated with other teams
  - Developed leadership and project management skills

Professional Training:
• ARAMBH Startup - Backend Team Trainee (Aug. 2024 – Oct. 2024)
  - Collaborated with ECE colleagues on backend projects using Python
  - Applied and enhanced Python skills through assigned tasks
  - Gained experience in startup environment

Social Impact:
• Hamari Pahchan NGO - Volunteered for 7-day charity drive (June 2023)
  - Fund raising, raising awareness, digital promotions
  - NGO provides education and skill development to underprivileged
  - Offers internships for students, contributing to social development

Marketing Experience:
• Corizo Company - Marketing Intern (June 2023 - July 2023)
  - Found and joined various online communities to market the Corizo group
  - Discovered that targeted topic-based groups (Sports, fitness, Cooking Classes, Founders office) yield more responsive results than general marketing groups
  - Learned digital marketing strategies and community engagement""",
                'metadata': {
                    'category': 'experience',
                    'subcategory': 'volunteer-leadership',
                    'importance': 7,
                    'tags': ['leadership', 'volunteer', 'ngo', 'marketing', 'graphdb', 'startup'],
                    'lastUpdated': datetime.now().isoformat(),
                    'source': 'portfolio.txt'
                }
            })

        return documents

    def extract_technical_skills(self) -> List[Dict]:
        """Extract comprehensive technical skills from portfolio.txt"""
        portfolio_txt_path = os.path.join(self.base_path, 'src', 'data', 'portfolio.txt')

        if not os.path.exists(portfolio_txt_path):
            return []

        with open(portfolio_txt_path, 'r', encoding='utf-8') as f:
            content = f.read()

        documents = []

        # Extract technical skills section
        skills_match = re.search(r'\*\*\*Technical Skills\*\*\*(.*?)(?=\*\*\*Professional & Volunteer Experience\*\*\*|$)', content, re.DOTALL)
        if skills_match:
            skills_content = skills_match.group(1).strip()
            documents.append({
                'id': 'skills-comprehensive-technical',
                'content': f"""Vansh's Comprehensive Technical Skills:

Programming Languages:
• Python - Primary language for AI/ML development, backend APIs, data processing
• C++ - Systems programming, performance optimization, algorithmic problem solving
• C - Low-level programming, system calls, memory management
• HTML/CSS - Frontend web development, responsive design
• SQL - Database queries, data manipulation, relational database design
• Cypher - Graph database query language for Neo4j

Developer Tools & Environments:
• VS Code - Primary IDE for development
• Vim - Text editor for efficient coding
• Git - Version control and collaboration
• Android Studio - Mobile app development environment
• AWS SageMaker - Machine learning model development and training
• AWS EC2 - Cloud computing instances
• S3 buckets - Cloud storage solutions
• Google Cloud Platform (GCP) - Cloud deployment and services
• Docker - Containerization and deployment

Technologies & Frameworks:
• Scikit-learn - Machine learning algorithms and data preprocessing
• Beautiful-Soup - Web scraping and HTML parsing
• TensorFlow - Deep learning model development and deployment
• OpenCV - Computer vision and image processing
• Computer Vision cv2 - Advanced image processing techniques
• Linux (Kali/Arch/Mint) - Operating system proficiency
• Google-Colab - Collaborative ML development environment
• Django - Web framework for backend development
• GraphDB - Graph database technologies
• GraphRAG - Graph-based retrieval augmented generation
• DataStax - Database management platform
• Make.com - Automation and integration platform
• LangGraph - Language model graph processing
• Ollama - Local LLM deployment

AI & Machine Learning:
• Large Language Models (LLMs): OpenAI, Gemini, DeepSeek
• Machine Learning algorithms and model development
• Natural Language Processing (NLP)
• Computer Vision applications
• Deep Learning architectures

AI Development Tools:
• GitHub Copilot - AI-powered code completion
• RooCode - AI development assistant
• Amazon Q Dev - AWS AI development tool
• cursorAI - AI-enhanced code editor

Cloud Platforms:
• AWS SageMaker - ML model training and deployment
• AWS EC2 - Scalable computing instances
• S3 buckets - Object storage service
• Google Cloud Platform - Comprehensive cloud services

Documentation & Productivity Tools:
• Napkin.ai - AI-powered documentation
• draw.io - Diagram and flowchart creation
• Notion - Project management and documentation

System Knowledge:
• Operating systems architecture and principles
• System calls and low-level programming
• Context switching and process management
• Machine dependencies and hardware optimization
• CPU and GPU utilization optimization
• Multiprocessing and parallel computing
• Thread management and concurrent programming

Academic Foundations:
• Advanced data structures and algorithms
• Compiler design and implementation
• Language parsers and syntax analysis
• Networking concepts and protocols
• Design and Analysis of Algorithms
• Operating system principles""",
                'metadata': {
                    'category': 'skills',
                    'subcategory': 'comprehensive-technical',
                    'importance': 10,
                    'tags': ['programming', 'ai-ml', 'cloud', 'tools', 'frameworks', 'systems'],
                    'lastUpdated': datetime.now().isoformat(),
                    'source': 'portfolio.txt'
                }
            })

        return documents

    def add_existing_knowledge_base_docs(self) -> List[Dict]:
        """Add existing structured knowledge base documents"""
        return [
            {
                'id': 'personal-contact-social',
                'content': """Vansh Oberoi's Contact Information and Social Presence:

Primary Contact:
• Full Name: Vansh Oberoi
• Address: 462 Model Town, Kapurthala, Punjab 144601, India
• Phone: +91 9646570760
• Email: <EMAIL>

Professional Social Media:
• LinkedIn: https://linkedin.com/in/Vansh462
• GitHub: https://github.com/Vansh462

Personal Social Media:
• Twitter: https://twitter.com/vansh462
• Instagram: https://www.instagram.com/vanshoberoi3103
• Facebook: https://www.facebook.com/solo.learn.33

Professional Identity:
Vansh maintains an active professional presence online, particularly on LinkedIn and GitHub where he showcases his technical projects and professional achievements. His GitHub profile contains numerous repositories demonstrating his coding skills and project portfolio.""",
                'metadata': {
                    'category': 'personal',
                    'subcategory': 'contact-social',
                    'importance': 9,
                    'tags': ['contact', 'social-media', 'linkedin', 'github', 'professional-presence'],
                    'lastUpdated': datetime.now().isoformat(),
                    'source': 'comprehensive-extraction'
                }
            },
            {
                'id': 'testimonials-professional',
                'content': """Professional Testimonials and Recommendations for Vansh Oberoi:

Nikhil (Working Professional, LearnFlu):
"Good Work on Jute Pest project."
LinkedIn: https://www.linkedin.com/in/nikhil-maurya-588945170/

Harshraj (CEO, Embea):
"Demonstrates disciplined and time-managed work delivery with good technical skills and strong potential. Shows excellent problem-solving abilities and should continue developing a broader strategic perspective to see projects through to completion."

Tarun (CEO, Tekno Solve):
"I appreciate your thinking approach and the mentality you bring to challenges. Your problem-solving mindset and technical perspective make you someone I would definitely like to collaborate with in future ventures."
LinkedIn: https://www.linkedin.com/in/tarun-singh666/

These testimonials highlight Vansh's:
• Strong technical skills and problem-solving abilities
• Disciplined work approach and time management
• Collaborative mindset and professional attitude
• Potential for strategic thinking and project completion
• Positive impact on professional relationships""",
                'metadata': {
                    'category': 'testimonials',
                    'subcategory': 'professional-recommendations',
                    'importance': 8,
                    'tags': ['testimonials', 'recommendations', 'professional-feedback', 'ceo-endorsements'],
                    'lastUpdated': datetime.now().isoformat(),
                    'source': 'portfolio-data'
                }
            }
        ]

def setup_chromadb():
    """Setup ChromaDB with comprehensive knowledge base"""
    print("🚀 Setting up Comprehensive ChromaDB Knowledge Base...")
    
    # Initialize ChromaDB client
    client = chromadb.PersistentClient(path="./chroma_data")
    
    # Delete existing collection if it exists
    try:
        client.delete_collection("vansh_portfolio_comprehensive")
        print("🗑️  Deleted existing collection")
    except:
        pass
    
    # Create new collection
    collection = client.create_collection(
        name="vansh_portfolio_comprehensive",
        metadata={"description": "Comprehensive knowledge base for Vansh Oberoi's portfolio"}
    )
    
    # Extract data from all sources
    extractor = PortfolioDataExtractor()

    print("📄 Extracting data from all sources...")
    print("  📋 Extracting basic info from portfolio.txt...")
    txt_docs = extractor.extract_from_portfolio_txt()

    print("  🚀 Extracting projects from portfolio.txt...")
    project_docs = extractor.extract_projects_from_txt()

    print("  🤝 Extracting volunteer experience...")
    volunteer_docs = extractor.extract_volunteer_experience()

    print("  💻 Extracting technical skills...")
    skills_docs = extractor.extract_technical_skills()

    print("  📚 Adding existing knowledge base documents...")
    existing_docs = extractor.add_existing_knowledge_base_docs()

    # Combine all documents
    all_documents = txt_docs + project_docs + volunteer_docs + skills_docs + existing_docs

    print(f"📊 Total extracted documents: {len(all_documents)}")
    print(f"  • Basic info: {len(txt_docs)}")
    print(f"  • Projects: {len(project_docs)}")
    print(f"  • Volunteer experience: {len(volunteer_docs)}")
    print(f"  • Technical skills: {len(skills_docs)}")
    print(f"  • Existing knowledge: {len(existing_docs)}")
    
    # Add documents to ChromaDB
    if all_documents:
        documents = [doc['content'] for doc in all_documents]
        metadatas = [doc['metadata'] for doc in all_documents]
        ids = [doc['id'] for doc in all_documents]
        
        collection.add(
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )
        
        print(f"✅ Added {len(all_documents)} documents to ChromaDB")
    
    # Verify the setup
    count = collection.count()
    print(f"📈 Total documents in knowledge base: {count}")
    
    # Test query
    print("\n🔍 Testing knowledge base with sample query...")
    results = collection.query(
        query_texts=["Tell me about Vansh's education"],
        n_results=2
    )
    
    if results['documents'][0]:
        print("✅ Knowledge base is working correctly!")
        print(f"Sample result: {results['documents'][0][0][:200]}...")
    else:
        print("❌ Knowledge base test failed")
    
    print(f"\n🎉 ChromaDB setup completed successfully!")
    print(f"📍 Data stored in: ./chroma_data")
    print(f"📊 Total documents: {count}")
    print(f"🔗 Collection name: vansh_portfolio_comprehensive")

if __name__ == "__main__":
    setup_chromadb()
