#!/usr/bin/env node

/**
 * ChromaDB Setup Script
 * This script sets up a local ChromaDB instance for the AI chatbot knowledge base
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up ChromaDB for AI Chatbot Knowledge Base...\n');

// Check if Python is installed
function checkPython() {
  return new Promise((resolve, reject) => {
    const python = spawn('python', ['--version']);
    
    python.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Python is installed');
        resolve(true);
      } else {
        // Try python3
        const python3 = spawn('python3', ['--version']);
        python3.on('close', (code) => {
          if (code === 0) {
            console.log('✅ Python3 is installed');
            resolve(true);
          } else {
            console.log('❌ Python is not installed. Please install Python 3.8+ first.');
            reject(false);
          }
        });
      }
    });
  });
}

// Install ChromaDB
function installChromaDB() {
  return new Promise((resolve, reject) => {
    console.log('📦 Installing ChromaDB...');
    
    const pip = spawn('pip', ['install', 'chromadb'], { stdio: 'inherit' });
    
    pip.on('close', (code) => {
      if (code === 0) {
        console.log('✅ ChromaDB installed successfully');
        resolve(true);
      } else {
        console.log('❌ Failed to install ChromaDB');
        reject(false);
      }
    });
    
    pip.on('error', (error) => {
      console.log('Trying with pip3...');
      const pip3 = spawn('pip3', ['install', 'chromadb'], { stdio: 'inherit' });
      
      pip3.on('close', (code) => {
        if (code === 0) {
          console.log('✅ ChromaDB installed successfully');
          resolve(true);
        } else {
          console.log('❌ Failed to install ChromaDB');
          reject(false);
        }
      });
    });
  });
}

// Create ChromaDB startup script
function createStartupScript() {
  const scriptContent = `#!/usr/bin/env python3
"""
ChromaDB Server Startup Script
Starts a local ChromaDB server for the AI chatbot knowledge base
"""

import chromadb
from chromadb.config import Settings
import uvicorn
import os

def start_chromadb_server():
    print("🚀 Starting ChromaDB server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("💾 Data will be persisted in: ./chroma_data")
    print("🛑 Press Ctrl+C to stop the server\\n")
    
    # Ensure data directory exists
    os.makedirs('./chroma_data', exist_ok=True)
    
    # Start the server
    try:
        uvicorn.run(
            "chromadb.app:app",
            host="localhost",
            port=8000,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\\n🛑 ChromaDB server stopped")
    except Exception as e:
        print(f"❌ Error starting ChromaDB server: {e}")
        print("💡 Try installing uvicorn: pip install uvicorn")

if __name__ == "__main__":
    start_chromadb_server()
`;

  fs.writeFileSync('start-chromadb.py', scriptContent);
  console.log('✅ Created ChromaDB startup script: start-chromadb.py');
}

// Create package.json scripts
function updatePackageJson() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }
    
    packageJson.scripts['chromadb:start'] = 'python start-chromadb.py';
    packageJson.scripts['chromadb:setup'] = 'node scripts/setup-chromadb.js';
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Updated package.json with ChromaDB scripts');
  }
}

// Create README for ChromaDB setup
function createReadme() {
  const readmeContent = `# ChromaDB Knowledge Base Setup

This directory contains the setup and configuration for the AI chatbot's ChromaDB knowledge base.

## Quick Start

1. **Install ChromaDB** (if not already done):
   \`\`\`bash
   npm run chromadb:setup
   \`\`\`

2. **Start ChromaDB Server**:
   \`\`\`bash
   npm run chromadb:start
   \`\`\`
   
   Or manually:
   \`\`\`bash
   python start-chromadb.py
   \`\`\`

3. **Access the chatbot** - The AI chatbot will automatically connect to ChromaDB when available

## What is ChromaDB?

ChromaDB is a vector database that enables semantic search and retrieval for the AI chatbot. It provides:

- **Semantic Search**: Find relevant information based on meaning, not just keywords
- **Persistent Storage**: Knowledge base data is stored locally and persists between sessions
- **Fast Retrieval**: Optimized for quick information lookup during conversations
- **Contextual Responses**: AI can provide more accurate and detailed responses

## Knowledge Base Content

The knowledge base contains comprehensive information about Vansh Oberoi:

- **Personal Information**: Contact details, location, professional summary
- **Technical Skills**: Programming languages, frameworks, tools with proficiency levels
- **Work Experience**: Current role at EaseMyMed, responsibilities, technologies used
- **Projects**: Detailed information about featured projects with links and achievements
- **Expertise Areas**: Specialized knowledge in AI integration, healthcare AI, voice AI

## Architecture

\`\`\`
User Query → ChromaDB Search → Relevant Context → Gemini AI → Enhanced Response
\`\`\`

## Fallback Mode

If ChromaDB is not available, the system automatically falls back to:
- In-memory knowledge base search
- Simple text matching algorithms
- Basic context retrieval

## Troubleshooting

### ChromaDB Server Won't Start
- Ensure Python 3.8+ is installed
- Install required dependencies: \`pip install chromadb uvicorn\`
- Check if port 8000 is available

### Knowledge Base Not Loading
- Verify ChromaDB server is running at http://localhost:8000
- Check browser console for connection errors
- Restart the development server

### Performance Issues
- ChromaDB data is stored in \`./chroma_data\` directory
- Clear data directory to reset knowledge base
- Restart ChromaDB server after clearing data

## Development

To modify the knowledge base:
1. Edit \`src/services/chromaKnowledgeBase.ts\`
2. Add new documents to the \`knowledgeDocuments\` array
3. Restart the application to reload the knowledge base

## Production Deployment

For production:
1. Set up ChromaDB server on your hosting platform
2. Update the ChromaDB client URL in the code
3. Ensure persistent storage is configured
4. Consider using ChromaDB Cloud for managed hosting
`;

  fs.writeFileSync('CHROMADB_README.md', readmeContent);
  console.log('✅ Created ChromaDB setup documentation');
}

// Main setup function
async function setup() {
  try {
    await checkPython();
    await installChromaDB();
    createStartupScript();
    updatePackageJson();
    createReadme();
    
    console.log('\n🎉 ChromaDB setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Start ChromaDB server: npm run chromadb:start');
    console.log('2. Start your development server: npm run dev');
    console.log('3. The AI chatbot will automatically use ChromaDB for enhanced responses');
    console.log('\n📖 See CHROMADB_README.md for detailed documentation');
    
  } catch (error) {
    console.log('\n❌ Setup failed. Please check the requirements and try again.');
    console.log('💡 Make sure Python 3.8+ is installed and pip is available.');
  }
}

// Run setup
setup();
