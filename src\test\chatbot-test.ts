// Test script for AI Chatbot functionality
// This script tests API key fetching and basic Gemini service functionality

import geminiService from '../services/geminiService';

// Test interface
interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: any;
}

class ChatbotTester {
  private results: TestResult[] = [];

  // Test API key fetching
  async testApiKeyFetching(): Promise<TestResult> {
    console.log('🔑 Testing API key fetching...');
    
    try {
      const apiKeyStatus = geminiService.getApiKeyStatus();
      
      if (apiKeyStatus.hasKey) {
        return {
          name: 'API Key Fetching',
          passed: true,
          message: `API key found from ${apiKeyStatus.source}`,
          details: apiKeyStatus
        };
      } else {
        return {
          name: 'API Key Fetching',
          passed: false,
          message: 'No API key found in environment variables',
          details: {
            checkedSources: ['VITE_GEMINI_API_KEY', 'GEMINI_API_KEY'],
            found: 'none'
          }
        };
      }
    } catch (error) {
      return {
        name: 'API Key Fetching',
        passed: false,
        message: `Error testing API key: ${error}`,
        details: { error }
      };
    }
  }

  // Test service initialization
  async testServiceInitialization(): Promise<TestResult> {
    console.log('🚀 Testing service initialization...');
    
    try {
      const isReady = geminiService.isReady();
      
      if (isReady) {
        return {
          name: 'Service Initialization',
          passed: true,
          message: 'Gemini service initialized successfully',
          details: { ready: true }
        };
      } else {
        return {
          name: 'Service Initialization',
          passed: false,
          message: 'Gemini service not ready',
          details: { ready: false }
        };
      }
    } catch (error) {
      return {
        name: 'Service Initialization',
        passed: false,
        message: `Error testing service initialization: ${error}`,
        details: { error }
      };
    }
  }

  // Test basic chat functionality
  async testBasicChat(): Promise<TestResult> {
    console.log('💬 Testing basic chat functionality...');

    try {
      if (!geminiService.isReady()) {
        return {
          name: 'Basic Chat',
          passed: false,
          message: 'Service not ready for chat testing - API key may be missing',
          details: {
            serviceReady: false,
            apiKeyStatus: geminiService.getApiKeyStatus()
          }
        };
      }

      const testMessage = "Tell me about Vansh's skills";
      console.log('Sending test message:', testMessage);

      const response = await geminiService.generateResponse(testMessage, [], '/');
      console.log('Received response:', response);

      if (response.response && response.response.length > 0) {
        return {
          name: 'Basic Chat',
          passed: true,
          message: 'Chat functionality working correctly',
          details: {
            testMessage,
            responseLength: response.response.length,
            responsePreview: response.response.substring(0, 100) + '...',
            hasNavigationCommand: !!response.navigationCommand,
            hasNavigationAction: !!response.navigationAction
          }
        };
      } else {
        return {
          name: 'Basic Chat',
          passed: false,
          message: 'Empty or invalid response received',
          details: { response }
        };
      }
    } catch (error) {
      console.error('Chat test error:', error);
      return {
        name: 'Basic Chat',
        passed: false,
        message: `Error testing chat functionality: ${error}`,
        details: {
          error: error.toString(),
          apiKeyStatus: geminiService.getApiKeyStatus()
        }
      };
    }
  }

  // Test navigation functionality
  async testNavigationIntent(): Promise<TestResult> {
    console.log('🧭 Testing navigation intent analysis...');
    
    try {
      const { navigationService } = await import('../services/navigationService');
      
      const testCases = [
        { message: "Show me his projects", expectedPath: "/projects" },
        { message: "What's his work experience?", expectedPath: "/experience" },
        { message: "How can I contact him?", expectedPath: "/contact" },
        { message: "Tell me about his background", expectedPath: "/about" }
      ];

      let passedTests = 0;
      const testResults: any[] = [];

      for (const testCase of testCases) {
        const intent = navigationService.analyzeNavigationIntent(testCase.message);
        const passed = intent?.path === testCase.expectedPath;
        
        if (passed) passedTests++;
        
        testResults.push({
          message: testCase.message,
          expected: testCase.expectedPath,
          actual: intent?.path || 'none',
          passed
        });
      }

      const allPassed = passedTests === testCases.length;

      return {
        name: 'Navigation Intent',
        passed: allPassed,
        message: `${passedTests}/${testCases.length} navigation tests passed`,
        details: { testResults, passedTests, totalTests: testCases.length }
      };
    } catch (error) {
      return {
        name: 'Navigation Intent',
        passed: false,
        message: `Error testing navigation intent: ${error}`,
        details: { error }
      };
    }
  }

  // Run all tests
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting AI Chatbot Tests...\n');

    // Run tests in sequence
    this.results.push(await this.testApiKeyFetching());
    this.results.push(await this.testServiceInitialization());
    this.results.push(await this.testBasicChat());
    this.results.push(await this.testNavigationIntent());

    // Print results
    this.printResults();
  }

  // Print test results
  private printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('========================\n');

    let passedCount = 0;
    
    this.results.forEach((result, index) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${result.name}: ${status}`);
      console.log(`   Message: ${result.message}`);
      
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
      console.log('');
      
      if (result.passed) passedCount++;
    });

    console.log(`Overall: ${passedCount}/${this.results.length} tests passed`);
    
    if (passedCount === this.results.length) {
      console.log('🎉 All tests passed! Chatbot is ready to use.');
    } else {
      console.log('⚠️  Some tests failed. Please check the configuration.');
    }

    // Environment setup instructions
    if (!this.results[0]?.passed) {
      console.log('\n📝 Setup Instructions:');
      console.log('======================');
      console.log('1. Set the GEMINI_API_KEY environment variable');
      console.log('2. For development, you can also use VITE_GEMINI_API_KEY in .env file');
      console.log('3. Get your API key from: https://makersuite.google.com/app/apikey');
      console.log('4. Example: export GEMINI_API_KEY="your-api-key-here"');
    }
  }

  // Get test results
  getResults(): TestResult[] {
    return this.results;
  }
}

// Export for use in other files
export { ChatbotTester, TestResult };

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  const tester = new ChatbotTester();
  tester.runAllTests().catch(console.error);
} else {
  // Browser environment - expose to window for manual testing
  (window as any).ChatbotTester = ChatbotTester;
  console.log('ChatbotTester available on window.ChatbotTester');
}
