import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Play, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { ChatbotTester, TestResult } from '@/test/chatbot-test';

const ChatbotTestPanel: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [hasRun, setHasRun] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    setHasRun(false);

    try {
      const tester = new ChatbotTester();
      await tester.runAllTests();
      setResults(tester.getResults());
      setHasRun(true);
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    );
  };

  const passedCount = results.filter(r => r.passed).length;
  const totalCount = results.length;

  return (
    <div className="fixed top-4 left-4 z-50 w-96 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Chatbot Test Panel
        </h3>
        <Button
          onClick={runTests}
          disabled={isRunning}
          size="sm"
          className="flex items-center space-x-2"
        >
          <Play className="w-4 h-4" />
          <span>{isRunning ? 'Running...' : 'Run Tests'}</span>
        </Button>
      </div>

      {isRunning && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-sm">Running tests...</span>
          </div>
        </div>
      )}

      {hasRun && (
        <div className="mb-4">
          <div className={`text-sm font-medium ${
            passedCount === totalCount 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-red-600 dark:text-red-400'
          }`}>
            {passedCount === totalCount ? '✅ All tests passed!' : `⚠️ ${passedCount}/${totalCount} tests passed`}
          </div>
        </div>
      )}

      <div className="space-y-3 max-h-64 overflow-y-auto">
        {results.map((result, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-3"
          >
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {result.name}
              </span>
              {getStatusIcon(result.passed)}
            </div>
            
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {result.message}
            </p>

            {result.details && (
              <details className="text-xs">
                <summary className="cursor-pointer text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                  View Details
                </summary>
                <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs overflow-x-auto">
                  {JSON.stringify(result.details, null, 2)}
                </pre>
              </details>
            )}
          </motion.div>
        ))}
      </div>

      {hasRun && passedCount < totalCount && (
        <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                Setup Required
              </p>
              <p className="text-yellow-700 dark:text-yellow-300 mb-2">
                To use the AI chatbot, set the GEMINI_API_KEY environment variable:
              </p>
              <code className="block p-2 bg-yellow-100 dark:bg-yellow-900/40 rounded text-xs">
                export GEMINI_API_KEY="your-api-key-here"
              </code>
              <p className="text-yellow-700 dark:text-yellow-300 mt-2 text-xs">
                Get your API key from: <a 
                  href="https://makersuite.google.com/app/apikey" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="underline hover:no-underline"
                >
                  Google AI Studio
                </a>
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatbotTestPanel;
