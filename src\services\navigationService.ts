import { NavigateFunction } from 'react-router-dom';

// Interface for navigation actions
export interface NavigationAction {
  type: 'navigate' | 'scroll' | 'highlight';
  path?: string;
  elementId?: string;
  delay?: number;
}

// Smart navigation service for AI chatbot
export class NavigationService {
  private navigate: NavigateFunction | null = null;

  // Set the navigate function from React Router
  setNavigate(navigateFunction: NavigateFunction) {
    this.navigate = navigateFunction;
  }

  // Analyze user message and determine navigation intent
  analyzeNavigationIntent(message: string): NavigationAction | null {
    const lowerMessage = message.toLowerCase();

    // Keywords mapping to navigation actions
    const navigationMap = [
      {
        keywords: ['experience', 'work', 'job', 'internship', 'career', 'employment', 'professional'],
        action: { type: 'navigate' as const, path: '/experience' }
      },
      {
        keywords: ['project', 'portfolio', 'work samples', 'demos', 'code', 'github'],
        action: { type: 'navigate' as const, path: '/projects' }
      },
      {
        keywords: ['contact', 'reach', 'email', 'phone', 'social', 'linkedin', 'hire'],
        action: { type: 'navigate' as const, path: '/contact' }
      },
      {
        keywords: ['about', 'background', 'personal', 'bio', 'story', 'education'],
        action: { type: 'navigate' as const, path: '/about' }
      },
      {
        keywords: ['home', 'main', 'overview', 'introduction', 'start'],
        action: { type: 'navigate' as const, path: '/' }
      },
      {
        keywords: ['skills', 'technologies', 'programming', 'languages', 'frameworks'],
        action: { type: 'navigate' as const, path: '/about', elementId: 'skills-section' }
      }
    ];

    // Find matching navigation intent
    for (const mapping of navigationMap) {
      if (mapping.keywords.some(keyword => lowerMessage.includes(keyword))) {
        return mapping.action;
      }
    }

    return null;
  }

  // Execute navigation action
  async executeNavigation(action: NavigationAction): Promise<boolean> {
    if (!this.navigate) {
      console.warn('Navigate function not set');
      return false;
    }

    try {
      // Add delay if specified
      if (action.delay) {
        await new Promise(resolve => setTimeout(resolve, action.delay));
      }

      switch (action.type) {
        case 'navigate':
          if (action.path) {
            this.navigate(action.path);
            
            // If there's an element to scroll to, do it after navigation
            if (action.elementId) {
              setTimeout(() => {
                this.scrollToElement(action.elementId!);
              }, 300); // Wait for navigation to complete
            }
            return true;
          }
          break;

        case 'scroll':
          if (action.elementId) {
            this.scrollToElement(action.elementId);
            return true;
          }
          break;

        case 'highlight':
          if (action.elementId) {
            this.highlightElement(action.elementId);
            return true;
          }
          break;
      }
    } catch (error) {
      console.error('Navigation error:', error);
      return false;
    }

    return false;
  }

  // Scroll to specific element
  private scrollToElement(elementId: string) {
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start',
        inline: 'nearest'
      });
    }
  }

  // Highlight element temporarily
  private highlightElement(elementId: string) {
    const element = document.getElementById(elementId);
    if (element) {
      // Add highlight class
      element.classList.add('ai-highlight');
      
      // Remove highlight after 3 seconds
      setTimeout(() => {
        element.classList.remove('ai-highlight');
      }, 3000);
    }
  }

  // Generate navigation suggestions for AI responses
  generateNavigationSuggestion(intent: string): string {
    const suggestions = {
      experience: "Would you like me to show you his work experience and internships?",
      projects: "Let me take you to his projects section to see his work samples.",
      contact: "I can show you his contact information and social profiles.",
      about: "Let me show you more details about Vansh's background.",
      skills: "Would you like to see his technical skills and proficiency levels?",
      home: "Let me take you back to the main overview page."
    };

    return suggestions[intent as keyof typeof suggestions] || "";
  }

  // Check if current path matches intent
  isCurrentPath(path: string): boolean {
    return window.location.pathname === path;
  }

  // Get current section for context
  getCurrentSection(): string {
    const path = window.location.pathname;
    const sectionMap: { [key: string]: string } = {
      '/': 'home',
      '/about': 'about',
      '/experience': 'experience',
      '/projects': 'projects',
      '/contact': 'contact'
    };
    
    return sectionMap[path] || 'unknown';
  }

  // Smart navigation with context awareness
  smartNavigate(userMessage: string, currentPath: string): NavigationAction | null {
    const intent = this.analyzeNavigationIntent(userMessage);
    
    if (!intent || !intent.path) {
      return null;
    }

    // Don't navigate if already on the target page
    if (intent.path === currentPath) {
      // If there's a specific element to scroll to, do that instead
      if (intent.elementId) {
        return { type: 'scroll', elementId: intent.elementId };
      }
      return null;
    }

    return intent;
  }
}

// Export singleton instance
export const navigationService = new NavigationService();

// CSS for highlighting (to be added to global styles)
export const highlightCSS = `
.ai-highlight {
  animation: ai-pulse 2s ease-in-out;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  border-radius: 8px;
}

@keyframes ai-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
  }
}
`;

export default navigationService;
