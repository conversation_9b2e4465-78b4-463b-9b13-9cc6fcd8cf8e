<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="<PERSON><PERSON> Oberoi - AI Engineer & ML Specialist. Portfolio showcasing projects in Python, Machine Learning, Django REST APIs, and Cloud Technologies." />
    <meta name="keywords" content="AI Engineer, Machine Learning, Python Developer, Django REST API, Cloud Technologies, Portfolio, Resume, Software Engineer, Data Science, TensorFlow, AWS, OpenAI, Gemini, Computer Vision, Deep Learning, Neural Networks, Artificial Intelligence, Full Stack Developer, Backend Developer" />
    <meta name="geo.region" content="IN-PB" />
    <meta name="geo.placename" content="Kapurthala, Punjab, India" />
    <meta name="geo.position" content="31.3800;75.3800" />
    <meta name="ICBM" content="31.3800, 75.3800" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <meta name="rating" content="general" />
    <meta name="distribution" content="global" />
    <meta name="coverage" content="worldwide" />
    <meta name="author" content="Vansh Oberoi" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="https://portfolio-vansh-oberois-projects.vercel.app/" />
    
    <!-- Open Graph meta tags -->
    <meta property="og:image" content="https://portfolio-vansh-oberois-projects.vercel.app/portfolio-screenshot-light.webp" />
    <meta property="og:image:secure_url" content="https://portfolio-vansh-oberois-projects.vercel.app/portfolio-screenshot-light.webp" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:type" content="image/webp" />
    <meta property="og:image:alt" content="Vansh Oberoi - AI Engineer Portfolio" />
    <!-- Alternative dark theme image -->
    <meta property="og:image" content="https://portfolio-vansh-oberois-projects.vercel.app/portfolio-screenshot-dark.webp" />
    <meta property="og:image:secure_url" content="https://portfolio-vansh-oberois-projects.vercel.app/portfolio-screenshot-dark.webp" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:type" content="image/webp" />
    <meta property="og:image:alt" content="Vansh Oberoi - AI Engineer Portfolio (Dark Theme)" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Vansh Oberoi | AI Engineer & Machine Learning Specialist" />
    <meta property="og:description" content="Passionate AI Engineer with expertise in Python, Machine Learning, Django REST APIs, and Cloud Technologies. Check out my projects and resume!" />
    <meta property="og:url" content="https://portfolio-vansh-oberois-projects.vercel.app/" />
    <meta property="og:site_name" content="Vansh Oberoi Portfolio" />
    <meta property="og:locale" content="en_US" />
    
    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@vansh462" />
    <meta name="twitter:creator" content="@vansh462" />
    <meta name="twitter:domain" content="portfolio-vansh-oberois-projects.vercel.app" />
    <meta name="twitter:title" content="Vansh Oberoi | AI Engineer & Machine Learning Specialist" />
    <meta name="twitter:description" content="Passionate AI Engineer with expertise in Python, Machine Learning, Django REST APIs, and Cloud Technologies. Check out my projects and resume!" />
    <meta name="twitter:image" content="https://portfolio-vansh-oberois-projects.vercel.app/portfolio-screenshot-light.webp" />
    <meta name="twitter:image:alt" content="Vansh Oberoi - AI Engineer Portfolio" />
    
    <!-- WhatsApp specific -->
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    
    <!-- LinkedIn specific -->
    <meta property="article:author" content="Vansh Oberoi" />
    <meta property="article:publisher" content="https://vanshoberoi.com" />
    
    <!-- Additional meta tags -->
    <meta name="thumbnail" content="https://portfolio-vansh-oberois-projects.vercel.app/portfolio-screenshot-light.webp" />
    <meta name="image" content="https://portfolio-vansh-oberois-projects.vercel.app/portfolio-screenshot-light.webp" />
    <meta name="theme-color" content="#3b82f6" />
    <meta name="msapplication-TileColor" content="#3b82f6" />
    <meta name="msapplication-TileImage" content="/icons/ms-icon-144x144.png" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="application-name" content="Vansh Oberoi Portfolio" />
    <meta name="msapplication-tooltip" content="AI Engineer & ML Specialist Portfolio" />
    <meta name="msapplication-starturl" content="/" />
    <meta name="msapplication-navbutton-color" content="#3b82f6" />
    <meta name="format-detection" content="telephone=yes" />
    <meta name="format-detection" content="address=yes" />
    <meta name="format-detection" content="email=yes" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdn.simpleicons.org" />
    <link rel="preconnect" href="https://images.unsplash.com" />
    <link rel="dns-prefetch" href="//www.google-analytics.com" />
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//vercel.com" />
    <link rel="author" href="/humans.txt" />
    <link rel="me" href="https://linkedin.com/in/Vansh462" />
    <link rel="me" href="https://github.com/Vansh462" />

    <!-- Apple PWA meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Vansh Portfolio" />
    <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg?v=3" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico?v=3" />
    <link rel="shortcut icon" href="/favicon.svg?v=3" />
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon.svg?v=3" />

    <!-- Preload critical resources -->
    <link rel="preload" href="/profile.webp" as="image" type="image/webp" />
    <link rel="preload" href="/portfolio-screenshot-light.webp" as="image" type="image/webp" />
    <link rel="preload" href="/patterns/topography.svg" as="image" type="image/svg+xml" />

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap&text=abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.,;:?!(){}[]" rel="stylesheet" />
    <style>
      /* Fallback font to prevent layout shifts */
      @font-face {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: local('Arial');
      }

      /* Critical CSS for above-the-fold content */
      :root {
        font-family: 'Inter', system-ui, sans-serif;
        line-height: 1.5;
        font-weight: 400;
      }

      body {
        margin: 0;
        padding: 0;
        background-color: #f3f4f6;
        color: #111827;
        min-height: 100vh;
        transition: background-color 0.3s, color 0.3s;
      }

      @media (prefers-color-scheme: dark) {
        body {
          background-color: #111827;
          color: #f3f4f6;
        }
      }

      /* Prevent layout shifts */
      .hero-section {
        min-height: 80vh;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
      }

      /* Optimize CLS for images */
      img {
        max-width: 100%;
        height: auto;
      }

      /* Profile image optimization */
      .profile-image {
        border-radius: 50%;
        object-fit: cover;
      }

      /* Optimize main content */
      main {
        display: block;
        width: 100%;
      }

      /* Optimize header */
      header {
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(8px);
      }
    </style>
    <title>Vansh Oberoi | AI Engineer & Machine Learning Specialist - Portfolio | Python Developer | Django Expert</title>
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": ["Person", "ProfilePage"],
      "name": "Vansh Oberoi",
      "alternateName": "Vansh462",
      "jobTitle": "AI Engineer & Machine Learning Specialist",
      "description": "Passionate AI Engineer with expertise in Python, Machine Learning, and Web Technologies",
      "url": "https://portfolio-vansh-oberois-projects.vercel.app/",
      "mainEntityOfPage": "https://portfolio-vansh-oberois-projects.vercel.app/",
      "image": {
        "@type": "ImageObject",
        "url": "https://portfolio-vansh-oberois-projects.vercel.app/profile.webp",
        "width": 400,
        "height": 400
      },
      "email": "<EMAIL>",
      "telephone": "+91 9646570760",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "462 Model Town",
        "addressLocality": "Kapurthala",
        "addressRegion": "Punjab",
        "postalCode": "144601",
        "addressCountry": "IN"
      },
      "nationality": "Indian",
      "birthPlace": "India",
      "worksFor": {
        "@type": "Organization",
        "name": "EaseMyMed",
        "url": "https://easemymed.com"
      },
      "alumniOf": {
        "@type": "EducationalOrganization",
        "name": "Guru Nanak Dev University",
        "address": "Amritsar, Punjab, India"
      },
      "sameAs": [
        "https://linkedin.com/in/Vansh462",
        "https://github.com/Vansh462",
        "https://twitter.com/vansh462",
        "https://www.facebook.com/solo.learn.33",
        "https://www.instagram.com/vanshoberoi3103",
        "https://kaggle.com/vanshoberoi3103"
      ],
      "knowsAbout": [
        "Artificial Intelligence",
        "Machine Learning",
        "Python Programming",
        "Django REST Framework",
        "Cloud Technologies",
        "TensorFlow",
        "AWS SageMaker",
        "Google Cloud Platform",
        "OpenAI API",
        "Computer Vision",
        "Deep Learning",
        "Neural Networks",
        "Data Science",
        "RESTful APIs",
        "Docker",
        "CI/CD"
      ],
      "hasOccupation": {
        "@type": "Occupation",
        "name": "AI Engineer",
        "occupationLocation": "India",
        "skills": "Python, Machine Learning, AI, Django, Cloud Technologies"
      },
      "award": "99% Accuracy in Medical Prescription Prediction Model",
      "seeks": "AI Engineering Opportunities, Machine Learning Projects, Software Development Roles"
    }
    </script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Vansh Oberoi Portfolio",
      "alternateName": "AI Engineer Portfolio",
      "url": "https://portfolio-vansh-oberois-projects.vercel.app/",
      "description": "Professional portfolio showcasing AI/ML projects, experience, and skills",
      "inLanguage": "en-US",
      "isAccessibleForFree": true,
      "author": {
        "@type": "Person",
        "name": "Vansh Oberoi"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://portfolio-vansh-oberois-projects.vercel.app/?search={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
              console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(error => {
              console.error('Service Worker registration failed:', error);
            });
        });
      }
    </script>
  </body>
</html>
