import { ChromaClient, Collection } from 'chromadb';

// Enhanced knowledge base data structure
export interface KnowledgeDocument {
  id: string;
  content: string;
  metadata: {
    category: string;
    subcategory?: string;
    importance: number; // 1-10 scale
    tags: string[];
    lastUpdated: string;
    source: string;
  };
}

// Comprehensive knowledge base data
export const knowledgeDocuments: KnowledgeDocument[] = [
  // Personal Information
  {
    id: 'personal-basic',
    content: '<PERSON><PERSON> is an AIML Development Engineer based in Model Town, Kapurthala, Punjab 144601, India. He can be <NAME_EMAIL> or +91 9646570760. He is passionate about AI development and software engineering.',
    metadata: {
      category: 'personal',
      subcategory: 'basic-info',
      importance: 10,
      tags: ['name', 'contact', 'location', 'title'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'personal-summary',
    content: '<PERSON><PERSON> is a passionate AI Developer and Software Engineer with expertise in Python, Machine Learning, Django REST APIs, and Cloud Technologies. He specializes in developing cutting-edge AI solutions, RESTful APIs, implementing RAG for contextual AI interactions, and integrating advanced AI technologies including OpenAI and Gemini models.',
    metadata: {
      category: 'personal',
      subcategory: 'summary',
      importance: 9,
      tags: ['summary', 'expertise', 'specialization'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'personal-social',
    content: 'Vansh maintains an active professional presence online. His LinkedIn profile is linkedin.com/in/Vansh462, GitHub at github.com/Vansh462, Twitter @vansh462, Instagram @vanshoberoi3103, and Facebook at facebook.com/solo.learn.33.',
    metadata: {
      category: 'personal',
      subcategory: 'social-media',
      importance: 7,
      tags: ['social-media', 'linkedin', 'github', 'contact'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },

  // Current Experience
  {
    id: 'experience-current',
    content: 'Vansh is currently working as an AIML Development Engineer Intern at EaseMyMed from December 2024 to June 2025. This is a healthcare technology company where he develops AI solutions for medical applications.',
    metadata: {
      category: 'experience',
      subcategory: 'current-role',
      importance: 10,
      tags: ['current-job', 'easemymed', 'intern', 'healthcare', 'ai'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'experience-responsibilities',
    content: 'At EaseMyMed, Vansh develops and manages RESTful APIs using Django REST framework for healthcare applications. He integrates AI technologies including OpenAI GPT-4o-mini and Gemini gemini-2.0-flash models. He has developed custom AI functions to deepen understanding of core principles, often bypassing LangChain for more direct implementations.',
    metadata: {
      category: 'experience',
      subcategory: 'responsibilities',
      importance: 9,
      tags: ['django', 'rest-api', 'openai', 'gemini', 'healthcare'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'experience-rag-implementation',
    content: 'Vansh has implemented RAG (Retrieval-Augmented Generation) systems to cross-reference and provide right context to AI in consecutive calls. He has integrated Bhashini AI voice models and implemented benchmarking and feedback mechanisms for voice AI applications.',
    metadata: {
      category: 'experience',
      subcategory: 'rag-ai',
      importance: 9,
      tags: ['rag', 'retrieval-augmented-generation', 'bhashini', 'voice-ai', 'context'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'experience-cloud-deployment',
    content: 'Vansh utilizes AWS SageMaker for development and Google Cloud Console for deployment with Docker and CloudBuild. He creates daily technical documentation on Notion and researches new feature prototypes. He has experience with processing and analyzing images, PDFs, and ZIP folders using JSON data structures.',
    metadata: {
      category: 'experience',
      subcategory: 'cloud-deployment',
      importance: 8,
      tags: ['aws', 'sagemaker', 'gcp', 'docker', 'cloudbuild', 'notion'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },

  // Technical Skills - Programming
  {
    id: 'skills-python',
    content: 'Vansh has expert-level Python skills with 95% proficiency. He has extensive experience in AI/ML development using Python, including frameworks like TensorFlow, scikit-learn, pandas, NumPy, and various AI libraries. Python is his primary programming language for AI and backend development.',
    metadata: {
      category: 'skills',
      subcategory: 'programming',
      importance: 10,
      tags: ['python', 'expert', 'ai-ml', 'tensorflow', 'scikit-learn'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'skills-cpp',
    content: 'Vansh has strong C++ skills with 85% proficiency, providing him with a solid foundation in systems programming. This knowledge helps him understand low-level optimizations and performance considerations in AI applications.',
    metadata: {
      category: 'skills',
      subcategory: 'programming',
      importance: 7,
      tags: ['cpp', 'systems-programming', 'performance'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'skills-javascript-typescript',
    content: 'Vansh has 80% proficiency in JavaScript and 75% in TypeScript, enabling him to work on both frontend and backend development. He uses these skills for creating modern web applications and APIs.',
    metadata: {
      category: 'skills',
      subcategory: 'programming',
      importance: 8,
      tags: ['javascript', 'typescript', 'frontend', 'backend', 'web-development'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },

  // AI/ML Skills
  {
    id: 'skills-machine-learning',
    content: 'Vansh has 90% proficiency in Machine Learning, with expertise in both supervised and unsupervised learning algorithms. He has practical experience with classification, regression, clustering, and dimensionality reduction techniques.',
    metadata: {
      category: 'skills',
      subcategory: 'ai-ml',
      importance: 10,
      tags: ['machine-learning', 'supervised', 'unsupervised', 'classification', 'regression'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'skills-deep-learning',
    content: 'Vansh has 85% proficiency in Deep Learning, with hands-on experience in neural networks, CNNs, and RNNs. He has implemented and deployed deep learning models using TensorFlow and has experience with computer vision applications.',
    metadata: {
      category: 'skills',
      subcategory: 'ai-ml',
      importance: 9,
      tags: ['deep-learning', 'neural-networks', 'cnn', 'rnn', 'tensorflow'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'skills-computer-vision',
    content: 'Vansh has 80% proficiency in Computer Vision with extensive experience using OpenCV. He has worked on image processing, object detection, and classification projects including pest classification and face recognition systems.',
    metadata: {
      category: 'skills',
      subcategory: 'ai-ml',
      importance: 8,
      tags: ['computer-vision', 'opencv', 'image-processing', 'object-detection'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },

  // Frameworks and Tools
  {
    id: 'skills-django',
    content: 'Vansh has 88% proficiency in Django and Django REST Framework. He uses Django for building robust web applications and RESTful APIs, particularly in healthcare applications at EaseMyMed.',
    metadata: {
      category: 'skills',
      subcategory: 'frameworks',
      importance: 9,
      tags: ['django', 'rest-framework', 'web-development', 'api'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'skills-cloud-platforms',
    content: 'Vansh has experience with cloud platforms including AWS SageMaker (60% proficiency) for ML model training and deployment, and Google Cloud Platform (80% proficiency) for cloud deployment and services. He also has 80% proficiency in Docker for containerization.',
    metadata: {
      category: 'skills',
      subcategory: 'cloud',
      importance: 8,
      tags: ['aws', 'sagemaker', 'gcp', 'docker', 'cloud-deployment'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },

  // Featured Projects
  {
    id: 'project-promptwizard',
    content: 'PromptWizard is one of Vansh\'s flagship projects where he designed and implemented a custom frontend UI from scratch for Microsoft\'s prompt optimizer. The project focuses on user flow and clarity, integrating backend API with a new UI. It features Vercel-first deployment following mono repo structure, deployable with a single command. Key features include Test Values for auto-filling sample data and instant output previews, plus multi-feature selection for optimized prompts. Live demo: https://prompt-wizard-three.vercel.app/, GitHub: https://github.com/Vansh462/PromptWizard',
    metadata: {
      category: 'projects',
      subcategory: 'featured',
      importance: 10,
      tags: ['promptwizard', 'microsoft', 'frontend', 'ui-ux', 'vercel', 'deployment'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'project-medical-prediction',
    content: 'Dr\'s Medicine Prescription Prediction is a machine learning project where Vansh built a supervised ML pipeline to predict appropriate medical prescriptions based on patient symptoms and demographic data. The project processed 5,900 patient records after data cleaning from an original dataset of 5,921 records. Through extensive exploratory data analysis (EDA) and feature engineering, he achieved over 99% test accuracy using a Random Forest model. The project demonstrates expertise in healthcare AI and data preprocessing. Available on Kaggle: https://kaggle.com/code/vanshoberoi3103/dr-s-medicine-prescription-prediction-model-99',
    metadata: {
      category: 'projects',
      subcategory: 'featured',
      importance: 9,
      tags: ['machine-learning', 'healthcare', 'prediction', 'random-forest', 'eda', 'kaggle'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'project-jute-pest',
    content: 'Jute Pest Classification showcases Vansh\'s deep learning expertise. He fine-tuned TensorFlow\'s ResNet101x1 model to classify 13 different jute pest types, achieving 95% accuracy on a test set of 379 images. The project used 6,443 training images and 443 validation images. He implemented advanced preprocessing techniques including cropping, resizing, and pixel normalization. The model was optimized on an AWS m5.large instance, achieving approximately 10 seconds per epoch through hyperparameter tuning and prefetch() optimization. GitHub: https://github.com/Vansh462/LearningProjects/tree/main/Jute%20Pest, Kaggle: https://www.kaggle.com/code/vanshoberoi3103/jute-pest-tf-restnet101x1-95-acc-on-1st-try',
    metadata: {
      category: 'projects',
      subcategory: 'featured',
      importance: 9,
      tags: ['deep-learning', 'tensorflow', 'resnet', 'computer-vision', 'aws', 'optimization'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'project-sports-classification',
    content: 'Sports Person Classification demonstrates Vansh\'s computer vision skills using traditional ML approaches. He engineered a face-based sports person classifier leveraging HaarCascades (OpenCV) and wavelet transforms for robust image preprocessing. The system includes face validation, cropping, and feature extraction. He conducted systematic hyperparameter optimization using GridSearchCV across SVC, RandomForest, and Logistic Regression models, utilizing StratifiedShuffleSplit for balanced validation. The final Logistic Regression classifier achieved 84.31% test accuracy.',
    metadata: {
      category: 'projects',
      subcategory: 'featured',
      importance: 8,
      tags: ['computer-vision', 'opencv', 'face-detection', 'classification', 'gridsearch', 'optimization'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },

  // Technical Expertise Areas
  {
    id: 'expertise-ai-integration',
    content: 'Vansh specializes in AI model integration, particularly with OpenAI GPT models and Google Gemini. He has hands-on experience integrating GPT-4o-mini and Gemini-2.0-flash models into production applications. He develops custom AI functions and implements RAG (Retrieval-Augmented Generation) systems for contextual AI interactions.',
    metadata: {
      category: 'expertise',
      subcategory: 'ai-integration',
      importance: 10,
      tags: ['openai', 'gemini', 'gpt', 'rag', 'ai-integration', 'production'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'expertise-healthcare-ai',
    content: 'Vansh has specialized experience in healthcare AI applications. At EaseMyMed, he develops AI solutions specifically for healthcare, including medical prescription prediction systems and healthcare data processing. He works with medical data structures and implements AI systems that can process and analyze medical information.',
    metadata: {
      category: 'expertise',
      subcategory: 'healthcare-ai',
      importance: 9,
      tags: ['healthcare', 'medical-ai', 'easemymed', 'medical-data', 'prescription'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  },
  {
    id: 'expertise-voice-ai',
    content: 'Vansh has experience with voice AI technologies, particularly Bhashini AI voice models. He has implemented benchmarking and feedback mechanisms for voice AI applications and integrated voice processing capabilities into healthcare applications.',
    metadata: {
      category: 'expertise',
      subcategory: 'voice-ai',
      importance: 7,
      tags: ['voice-ai', 'bhashini', 'speech-processing', 'benchmarking'],
      lastUpdated: '2024-12-01',
      source: 'portfolio'
    }
  }
];

// ChromaDB Knowledge Base Service
export class ChromaKnowledgeBase {
  private client: ChromaClient;
  private collection: Collection | null = null;
  private isInitialized = false;

  constructor() {
    this.client = new ChromaClient({
      path: "http://localhost:8000" // Default ChromaDB server
    });
  }

  // Initialize the knowledge base
  async initialize(): Promise<void> {
    try {
      console.log('Initializing ChromaDB knowledge base...');
      
      // Create or get collection
      this.collection = await this.client.getOrCreateCollection({
        name: "vansh_portfolio_knowledge",
        metadata: { description: "Comprehensive knowledge base for Vansh Oberoi's portfolio" }
      });

      // Check if collection is empty and populate if needed
      const count = await this.collection.count();
      if (count === 0) {
        await this.populateKnowledgeBase();
      }

      this.isInitialized = true;
      console.log('ChromaDB knowledge base initialized successfully');
    } catch (error) {
      console.error('Failed to initialize ChromaDB:', error);
      console.log('Falling back to in-memory knowledge base');
    }
  }

  // Populate the knowledge base with documents
  private async populateKnowledgeBase(): Promise<void> {
    if (!this.collection) return;

    console.log('Populating knowledge base with documents...');

    const documents = knowledgeDocuments.map(doc => doc.content);
    const metadatas = knowledgeDocuments.map(doc => doc.metadata);
    const ids = knowledgeDocuments.map(doc => doc.id);

    await this.collection.add({
      documents,
      metadatas,
      ids
    });

    console.log(`Added ${documents.length} documents to knowledge base`);
  }

  // Query the knowledge base
  async query(queryText: string, nResults: number = 5): Promise<any> {
    if (!this.isInitialized || !this.collection) {
      console.log('ChromaDB not available, using fallback search');
      return this.fallbackSearch(queryText, nResults);
    }

    try {
      const results = await this.collection.query({
        queryTexts: [queryText],
        nResults
      });

      return {
        documents: results.documents[0] || [],
        metadatas: results.metadatas[0] || [],
        distances: results.distances?.[0] || [],
        ids: results.ids[0] || []
      };
    } catch (error) {
      console.error('ChromaDB query failed:', error);
      return this.fallbackSearch(queryText, nResults);
    }
  }

  // Fallback search using simple text matching
  private fallbackSearch(queryText: string, nResults: number) {
    const query = queryText.toLowerCase();
    const scored = knowledgeDocuments
      .map(doc => ({
        ...doc,
        score: this.calculateRelevanceScore(doc, query)
      }))
      .filter(doc => doc.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, nResults);

    return {
      documents: scored.map(doc => doc.content),
      metadatas: scored.map(doc => doc.metadata),
      distances: scored.map(doc => 1 - doc.score), // Convert score to distance
      ids: scored.map(doc => doc.id)
    };
  }

  // Calculate relevance score for fallback search
  private calculateRelevanceScore(doc: KnowledgeDocument, query: string): number {
    const content = doc.content.toLowerCase();
    const tags = doc.metadata.tags.join(' ').toLowerCase();
    const category = doc.metadata.category.toLowerCase();
    
    let score = 0;
    
    // Exact phrase matches
    if (content.includes(query)) score += 10;
    
    // Tag matches
    if (tags.includes(query)) score += 8;
    
    // Category matches
    if (category.includes(query)) score += 6;
    
    // Word matches
    const queryWords = query.split(' ');
    queryWords.forEach(word => {
      if (word.length > 2) {
        if (content.includes(word)) score += 2;
        if (tags.includes(word)) score += 3;
      }
    });
    
    // Importance weighting
    score *= (doc.metadata.importance / 10);
    
    return score;
  }

  // Get documents by category
  async getByCategory(category: string): Promise<KnowledgeDocument[]> {
    if (!this.isInitialized || !this.collection) {
      return knowledgeDocuments.filter(doc => doc.metadata.category === category);
    }

    try {
      const results = await this.collection.get({
        where: { category }
      });

      return results.ids.map((id, index) => ({
        id,
        content: results.documents?.[index] || '',
        metadata: results.metadatas?.[index] as any
      }));
    } catch (error) {
      console.error('Failed to get documents by category:', error);
      return knowledgeDocuments.filter(doc => doc.metadata.category === category);
    }
  }

  // Check if ChromaDB is available
  isAvailable(): boolean {
    return this.isInitialized;
  }

  // Get all available categories
  getCategories(): string[] {
    return [...new Set(knowledgeDocuments.map(doc => doc.metadata.category))];
  }

  // Get enhanced context for AI responses
  async getEnhancedContext(query: string): Promise<string> {
    const results = await this.query(query, 3);
    
    if (!results.documents || results.documents.length === 0) {
      return 'No specific information found for this query.';
    }

    let context = 'Relevant information about Vansh:\n\n';
    
    results.documents.forEach((doc: string, index: number) => {
      const metadata = results.metadatas[index];
      context += `${doc}\n\n`;
    });

    return context;
  }
}

// Export singleton instance
export const chromaKnowledgeBase = new ChromaKnowledgeBase();
export default chromaKnowledgeBase;
