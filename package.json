{"name": "v_latest", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "lint": "eslint .", "preview": "vite preview --port 3000", "serve": "serve -s dist -p 3000", "optimize-images": "node scripts/optimize-images.js", "chromadb:setup": "node scripts/setup-chromadb.js", "chromadb:start": "python start-chromadb.py"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.1", "@phosphor-icons/react": "^2.1.7", "@vercel/analytics": "^1.5.0", "canvas-confetti": "^1.9.3", "chromadb": "^3.0.7", "framer-motion": "^12.6.3", "lucide-react": "^0.487.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-ga4": "^2.1.0", "react-router-dom": "^7.5.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/canvas-confetti": "^1.9.0", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "glob": "^10.3.10", "globals": "^15.15.0", "postcss": "^8.5.3", "serve": "^14.2.4", "sharp": "^0.33.3", "tailwindcss": "^3.4.17", "terser": "^5.39.0", "typescript": "^5.8.3", "vite": "^6.2.0"}}